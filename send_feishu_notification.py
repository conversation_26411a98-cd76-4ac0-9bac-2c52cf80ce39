import os
import requests
from datetime import datetime
import argparse
import re

parser = argparse.ArgumentParser()
parser.add_argument(
    "--all_success_result",
    default="baijian_spider.py:::2024-06-28 12:25:41 - INFO - ===new_record===百简, 商品数:17__EOF__",
    help="成功的日志集合, 用 __EOF__ 分割",
)
parser.add_argument(
    "--all_failed_result",
    default="",
    help="失败的日志集合, 用 __EOF__ 分割",
)
parser.add_argument(
    "--time_cost_in_seconds",
    default="21",
    help="任务耗时",
)
args, unknown = parser.parse_known_args()

FEISHU_NOTIFY_TOKEN = os.environ.get("FEISHU_NOTIFY_TOKEN", "4c945cb4-1ab8-450d-bb78-89db0f578cba")
formarter = "%H:%M:%S"
started_at = datetime.now().timestamp()

status = {}
failed_status = {}


def get_time_info_from_log(
    log_string="2024-06-28 12:25:41 - INFO - ===new_record===百简, 商品数:17",
):
    # Define a regular expression pattern to match the time only
    pattern = r"\d{2}:\d{2}:\d{2}"

    # Search for the pattern in the log string
    match = re.search(pattern, log_string)

    # Extract the time information
    if match:
        time_info = match.group()
        return time_info
    else:
        print("No time information found in the string.")
        return "00:00:00"


if args.all_success_result is not None and len(args.all_success_result) > 1:
    for success_result in args.all_success_result.split("__EOF__"):
        print("success_result", success_result)
        if len(success_result) <= 5:
            print("this should be the last one.")
            continue

        file_result = f"{success_result}".split(":::")
        print("file_result:", file_result)

        # 新格式：brand_name:::count, timestamp
        # file_result[0] = brand_name
        # file_result[1] = "count, timestamp"
        if len(file_result) >= 2:
            brand_name = file_result[0]
            count_and_time = file_result[1]  # 例如: "51, 2025-06-23 03:32:28"

            # 解析数量和时间
            if ", " in count_and_time:
                parts = count_and_time.split(", ", 1)
                record_count = parts[0]
                finished_time = parts[1] if len(parts) > 1 else ""
            else:
                record_count = count_and_time
                finished_time = ""

            print(f"{brand_name}, 记录数:{record_count}, 完成时间:{finished_time}")
            status[brand_name] = f"{record_count}条记录, {finished_time}"
        else:
            print(f"警告: 无法解析成功结果格式: {success_result}")
            # 兜底处理
            status[success_result] = "解析失败"

if args.all_failed_result is not None and len(args.all_failed_result) > 1:
    print(f"失败的文件列表:{args.all_failed_result}")
    for failed_result in args.all_failed_result.split("__EOF__"):
        print("failed_result:", failed_result)
        if len(failed_result) <= 5:
            print("this should be the last one.")
            continue
        if ":::" in failed_result:
            file_result = f"{failed_result}".split(":::")
            if len(file_result) >= 2:
                file_name = file_result[0]
                error_and_log = file_result[1]  # 例如: "未爬取到数据, 2025-06-24 02:22:45|||异常信息"

                # 检查是否包含日志信息（用|||分隔）
                if "|||" in error_and_log:
                    error_part, log_part = error_and_log.split("|||", 1)
                    # 格式化失败信息，包含日志
                    failed_status[file_name] = f"{error_part}\n    📄 日志: {log_part}"
                else:
                    # 没有日志信息，只显示错误
                    failed_status[file_name] = error_and_log
            else:
                failed_status[failed_result] = "解析失败"
        else:
            failed_status[failed_result] = ""


print(failed_status, status)

# https://open.feishu.cn/open-apis/bot/v2/hook/4c945cb4-1ab8-450d-bb78-89db0f578cba
print(
    f"status:{status},  failed_status:{failed_status}, time cost:{args.time_cost_in_seconds}s"
)

text_content = []
# 添加成功的爬虫信息，包含爬虫名称
for key, value in status.items():
    text_content.append(f"- {key}: {value}\n")

# 添加失败的爬虫信息
if len(failed_status.items()) > 0:
    text_content.append("\n\n**以下爬虫失败:**\n")
    for key, value in failed_status.items():
        text_content.append(f"- {key}, {value}\n")


def send_feishu_notice_with_title_and_content(feishu_url, title, markdown_content):
    headers = {"Content-Type": "application/json"}
    data = {
        "msg_type": "interactive",
        "card": {
            "header": {
                "template": "blue",
                "title": {
                    "content": f"**{title}**",
                    "tag": "lark_md",
                },
            },
            "elements": [
                {
                    "tag": "markdown",
                    "content": markdown_content,
                }
            ],
        },
    }
    feishu_result = requests.post(
        url=feishu_url, json=data, headers=headers, verify=False, proxies={}
    ).json()
    return feishu_result


time_cost = int(args.time_cost_in_seconds)

markdown_content = f"{''.join(text_content)}\n\n**任务耗时:** {int(time_cost/3600)}时{int((time_cost%3600)/60)}分{time_cost%60}秒"

print(f"markdown_content:{markdown_content}")

if len(FEISHU_NOTIFY_TOKEN) > 5:
    # 通知飞书
    headers = {"Content-Type": "application/json"}

    # Post the data
    url = f"https://open.feishu.cn/open-apis/bot/v2/hook/{FEISHU_NOTIFY_TOKEN}"
    feishu_result = send_feishu_notice_with_title_and_content(
        feishu_url=url,
        title=f"成功爬取了{len(status.items())}家竞对数据",
        markdown_content=markdown_content,
    )
    print(f"通知飞书结果:{feishu_result}")
else:
    print(f"无需飞书通知:{FEISHU_NOTIFY_TOKEN}")
