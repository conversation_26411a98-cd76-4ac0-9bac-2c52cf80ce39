import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy_json,
    write_pandas_df_into_odps,logging,
    get_odps_sql_result_as_df,
    THREAD_CNT,
)
import os

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

headers = {
    "uniacid": "2595",
    "appType": "mini",
    "Referer": "https://servicewechat.com/wx92c8f2cd458916b5/36/page-frame.html",
}
brand_name = "云果定制"
competitor_name_en = "yunguodingzhi"

logging.info(f"{time_of_now}, headers:{headers}")

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
logging.info(f"time_of_now:{time_of_now},headers:{headers}")
# 先登录
login = {
    # "username": "蜜菓",
    # "pwd": "123456",
    "username": "COEFEEBAR",
    "pwd": "123456a",
    "module_id": "5A612570-3E60-496F-AF51-9E680FE9CD9E",
    "access_token": "",
    "role": "member",
    "check_url_id": "",
}

token = requests.post(
    "https://ytgydhy.mdydt.net/api/Login/Login", json=login, verify=False
).json()
logging.info(token)
token_string = token["Data"]["access_token"]

# 获取类目列表
body = {
    "app_type": "1",
    "access_token": token_string,
    "role": "member",
    "check_url_id": "5432ef495f6147e1b726ea9b663441fa",
}

category_list = requests.post(
    "https://ytgydhy.mdydt.net/api/ShopAPI/Product/GetCategorysList",
    json=body,
    verify=False,
).json()["Data"]["categorys_list_stage"]
logging.info(category_list)

# 获取所有类目的商品：
params = {
    "flag": "0",
    "standard": "",
    "app_type": "1",
    "key_words": "",
    "request_source": "products_product-list",
    "category_id": "2",
    "max_sale_price": "",
    "min_sale_price": "",
    "is_count_down": "false",
    "is_group_buy": "false",
    "onlyinstock": "",
    "tagids": "",
    "activity_id": "",
    "brands": "",
    "sort_order_by": "DisplaySequence",
    "sort_order": "Asc",
    "page_index": "1",
    "page_size": "100",
    "access_token": token_string,
    "role": "member",
    "check_url_id": "5432ef495f6147e1b726ea9b663441fa",
}

product_list_all = []
for category in category_list:
    name = category["Name"]
    CategoryId = category["CategoryId"]
    params["category_id"] = CategoryId
    product_list = requests.post(
        "https://ytgydhy.mdydt.net/api/ShopAPI/ProductInfos/GetProductsList",
        verify=False,
        json=params,
    ).json()["Data"]["ProducetList"]
    if len(product_list) <=0:
        logging.info(f"类目:{name}, 商品数:{len(product_list)}")
        continue

    logging.info(f"类目:{name}, 商品数:{len(product_list)}, 商品:{product_list[0]}")
    product_list_all.extend(product_list)

product_list_all_df = pd.DataFrame(product_list_all)

# 写入odps
product_list_all_df["competitor"] = brand_name
all_products_df = product_list_all_df.astype(str)

today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_result_df"

result = write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(*) as records 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50"""
)

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    logging.info(f"===new_record==={brand_name}, 商品数:{len(product_list_all)}")
else:
    logging.info(f"{brand_name}, 写入ODPS失败")
