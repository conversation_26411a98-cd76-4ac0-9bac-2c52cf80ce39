import requests
import json
import hashlib
import time
from datetime import datetime, timedelta
import pandas as pd
import os
from odps import ODPS, DataFrame
from odps.accounts import StsAccount
import traceback
import concurrent.futures
import threading
import requests
import random
from proxy_setup import (
    get_remote_data_with_proxy,
    write_pandas_df_into_odps,
    logging,
    get_odps_sql_result_as_df,
    THREAD_CNT,
)

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
date_of_now = datetime.now().strftime("%Y-%m-%d")


def get_sub_cate_list_by_root_cate(app_and_kdt_id, root_cate):
    root_cate_alias = root_cate["alias"]
    get_second_cate_url = f"https://h5.youzan.com/wscdeco/feature-detail.json?{app_and_kdt_id}&alias={root_cate_alias}&check_chainstore=true&stage=16&check_multi_store=1&close_chainstore_webview_limit=true&check_old_home=1"
    second_cate_list = get_remote_data_with_proxy(get_second_cate_url)
    logging.info(
        f"{app_and_kdt_id} ,二级类目:{second_cate_list}, root_cate_alias:{root_cate_alias}"
    )

    tag_list_left = None
    for component in json.loads(second_cate_list)["data"]["components"]:
        logging.info(component["type"])
        if component["type"] == "tag_list_left":
            tag_list_left = component["tags"]
            break

    logging.info(tag_list_left)
    return tag_list_left


import json

testing = 200 * 10000  # 最多50万


# Parse the JSON data
def parsed_sku_data(product):
    if not "product_details" in product:
        logging.info("product has no product_details...")
        product["sku_obj_list"] = []
        return
    skuInfo = product["product_details"]["goodsData"]["skuInfo"]

    # Extracting SKU information
    skus = skuInfo.get("skus", [])
    sku_prices = skuInfo.get("skuPrices", [])
    sku_stocks = skuInfo.get("skuStocks", [])
    sku_specs = {
        str(item["id"]): item["name"]
        for prop in skuInfo.get("props", [])
        for item in prop.get("v", [])
    }

    # Creating a list to store extracted SKU information
    extracted_skus = []

    # Iterating through each SKU entry
    for sku in skus:
        sku_id = sku.get("skuId")
        sku_stock = next(
            (stock["stockNum"] for stock in sku_stocks if stock["skuId"] == sku_id),
            None,
        )
        sku_spec_id = sku.get("s1")
        sku_spec = sku_specs.get(sku_spec_id)
        sku_price = next(
            (price["price"] for price in sku_prices if price["skuId"] == sku_id), None
        )

        # Creating a dictionary for each SKU
        extracted_sku = {
            "skuId": sku_id,
            "skuStock": sku_stock,
            "skuSpec": sku_spec,
            "skuPrice": sku_price,
        }
        extracted_skus.append(extracted_sku)

    # Printing the extracted SKU information
    if len(extracted_skus) <= 0:
        spuPrice = skuInfo.get("spuPrice", {}).get("price", 0)
        skuIdFromSPU = skuInfo.get("spuPrice", {}).get("skuId", "")
        spuStock = skuInfo.get("spuStock", {}).get("stockNum", 0)
        extracted_skus.append(
            {
                "skuId": product["alias"],
                "skuStock": spuStock,
                "skuSpec": f"无子SKU, 见标题, skuId:{skuIdFromSPU}",
                "skuPrice": spuPrice,
            }
        )
        logging.info(
            f"未获取到SKU库存，请查看SPU库存:{skuInfo}, extracted_skus:{extracted_skus}"
        )
    logging.info(f"product:{product['alias']}, sku list:{extracted_skus}")
    product["sku_obj_list"] = extracted_skus


def get_product_detail(app_and_kdt_id, alias):
    try:
        detail_url = f"https://h5.m.youzan.com/wscgoods/tee-app/detail.json?{app_and_kdt_id}&bizEnv=retail&mpVersion=3.113.15&alias={alias}"
        detail_url = f"{detail_url}&slg=tagGoodList-default%2COpBottom%2C11927391377%2CabTraceId&banner_id=f.103346086~tag_list_left.1~0~Y1qPtmOd&oid=0&scene=1089"
        detail_url = f"{detail_url}&ump_alias=&ump_type=&activityId=&activityType=&subKdtId=0&fullPresaleSupportCart=true&platform=weixin&client=weapp&isGoodsWeappNative=1&withoutSkuDirectOrder=1"
        product_detail = get_remote_data_with_proxy(detail_url)
        return json.loads(product_detail)
    except Exception as e:
        logging.info(f"获取{alias}的商品详情失败：{e}")


def getSkuPrice(sku):
    try:
        return sku["skuPrice"] / 100.00
    except:
        return None


def process_product(product, app_and_kdt_id):
    global testing

    testing -= 1
    if testing <= 0:
        logging.info(f"停止了:{testing}")
        return
    if "product_details" in product:
        logging.info("已经获取过了")
        return

    current_thread_name = threading.current_thread().name
    logging.info(
        f"Processing product {product['alias']} in thread {current_thread_name}"
    )
    product_details = get_product_detail(
        app_and_kdt_id=app_and_kdt_id, alias=product["alias"]
    )

    if "data" in product_details:
        product["product_details"] = product_details["data"]
        parsed_sku_data(product)
    else:
        logging.info(
            f"product_details has no 'data' field:{product_details}, alias:{product['alias']}"
        )


def scrapy_and_save_into_odps(
    app_and_kdt_id, brand_name, root_cate_alias, competitor_name_en="-"
):
    logging.info(
        f"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}, root_cate_alias:{root_cate_alias}"
    )
    cate_list = get_remote_data_with_proxy(
        f"https://h5.youzan.com/wscdeco/feature-detail.json?{app_and_kdt_id}"
        + "&access_token=1f0bfa99cd3a06af049b7b0fd27160&stage=16&check_multi_store=1&close_chainstore_webview_limit=true"
        + f"&check_old_home=1&hadEnterShop=true&alias={root_cate_alias}"
        + "&check_chainstore=true&version_control=%7B%22use_native_feature_page%22%3A1%2C%22feature_page_path%22%3A%22pages%2Fhome%2Ftab%2Fone%22%7D"
    )
    logging.info(cate_list)
    cate_list = json.loads(cate_list)

    root_cate_list = []
    for component in cate_list["data"]["components"]:
        logging.info(component["type"])
        if component["type"] == "top_nav":
            logging.info(component["sub_entry"])
            root_cate_list = component["sub_entry"]

    all_sub_cate_list = []
    for root_cate in root_cate_list:
        sub_cate_list = get_sub_cate_list_by_root_cate(
            app_and_kdt_id=app_and_kdt_id, root_cate=root_cate
        )
        all_sub_cate_list.append(
            {"root_cate": root_cate, "sub_cate_list": sub_cate_list}
        )

    all_products = []
    for cate in all_sub_cate_list:
        logging.info(cate)
        cate_info = cate["root_cate"]
        sub_cate_list = cate["sub_cate_list"]
        for tag in sub_cate_list:
            alias = tag["alias"]
            url = f"https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?{app_and_kdt_id}&page=1&alias={alias}&json=1&offlineId=0&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1"
            cate_product_list = get_remote_data_with_proxy(url)
            cate_product_list = json.loads(cate_product_list)
            logging.info(
                f"alias:{alias} 商品个数:{len(cate_product_list['data']['list'])}"
            )
            for product in cate_product_list["data"]["list"]:
                product["类目名"] = cate_info["title"]
                product["二级类目名"] = tag["title"]
                product["类目link"] = cate_info["link_url"]
                all_products.append(product)

    if len(all_products) <= 0:
        logging.info("没有商品！")
        return
    logging.info(all_products[0])

    cate_list_to_save = []
    for root_cate in all_sub_cate_list:
        cate_list_to_save.append(
            {
                "一级类目": root_cate["root_cate"]["title"],
                "二级类目": "",
                "alias": root_cate["root_cate"]["alias"],
                "link": root_cate["root_cate"]["link_url"],
                "数据获取时间": time_of_now,
            }
        )
        for sub_cate in root_cate["sub_cate_list"]:
            cate_list_to_save.append(
                {
                    "一级类目": root_cate["root_cate"]["title"],
                    "二级类目": sub_cate["title"],
                    "alias": sub_cate["alias"],
                    "link": "",
                    "数据获取时间": time_of_now,
                }
            )

    with concurrent.futures.ThreadPoolExecutor(max_workers=THREAD_CNT) as executor:
        # Submit tasks to the executor
        futures = [
            executor.submit(process_product, product, app_and_kdt_id)
            for product in all_products
        ]

        # Wait for all tasks to complete
        concurrent.futures.wait(futures)

    all_products_clean = []
    for product in all_products:
        for item_tag in [
            "tagsOpt",
            "oPriceOpt",
            "priceOpt",
            "imgOpt",
            "titleOpt",
            "subTitleOpt",
            "extraInfo",
            "goodsPreloadOpt",
            "actionOpt",
            "extOpt",
        ]:
            product[item_tag] = product["itemCardOpt"][item_tag]
        clean_product = {}
        clean_product["title"] = product["titleOpt"]["title"]
        clean_product["类目名"] = product["类目名"]
        clean_product["二级类目名"] = product["二级类目名"]
        clean_product["price"] = product["priceOpt"]["price"]
        # logging.info(product['oPriceOpt'],product['url'],product)
        if "price" in product["oPriceOpt"]:
            clean_product["origin_price"] = product["oPriceOpt"]["price"]
        else:
            clean_product["origin_price"] = "NotFound"
        clean_product["img"] = product["imgOpt"]["src"]
        clean_product["url"] = product["url"]
        clean_product["alias"] = product["alias"]
        clean_product["id"] = product["id"]
        clean_product["类目link"] = product["类目link"]
        if "sku_obj_list" in product:
            clean_product["skuInfo"] = product["sku_obj_list"]
        else:
            clean_product["skuInfo"] = []
        sku_info = clean_product["skuInfo"]
        if len(sku_info) <= 0:
            spuPrice = sku_info.get("spuPrice", {}).get(
                "price", clean_product.get("price", 0)
            )
            skuIdFromSPU = sku_info.get("spuPrice", {}).get("skuId", "")
            spuStock = sku_info.get("spuStock", {}).get("stockNum", 0)
            sku_info = [
                {
                    "skuId": clean_product["alias"],
                    "skuStock": spuStock,
                    "skuSpec": f"无子SKU,见标题,skuId:{skuIdFromSPU}",
                    "skuPrice": spuPrice,
                }
            ]
            logging.info(f"使用了SPU的stock和price:{sku_info}")
        clean_product["skuInfo"] = sku_info
        clean_product["数据获取时间"] = time_of_now
        all_products_clean.append(clean_product)

    all_products_raw_df = []
    for obj in all_products:
        json_str = json.dumps(obj)
        hash_code = hashlib.md5(json_str.encode()).hexdigest()
        all_products_raw_df.append({"hash": hash_code, "json": json_str})
    all_products_raw_df = pd.DataFrame(all_products_raw_df)

    all_products_clean_df = pd.DataFrame(all_products_clean)

    df_exploded = all_products_clean_df.explode("skuInfo")

    df_exploded["skuId"] = df_exploded["skuInfo"].apply(lambda x: x["skuId"])
    df_exploded["skuStock"] = df_exploded["skuInfo"].apply(lambda x: x["skuStock"])
    df_exploded["skuSpec"] = df_exploded["skuInfo"].apply(lambda x: x["skuSpec"])
    df_exploded["skuPrice"] = df_exploded["skuInfo"].apply(lambda x: getSkuPrice(x))

    # Dropping the original 'skuInfo' column
    df_exploded.drop(columns=["skuInfo"], inplace=True)
    df_exploded.drop_duplicates(subset=["alias", "skuId"], inplace=True)
    df_exploded["competitor"] = brand_name
    df_exploded = df_exploded.astype(str)

    logging.info(df_exploded.columns)

    # 写入odps
    # odps_df = DataFrame(df_exploded)
    # all_products_raw_df = DataFrame(all_products_raw_df)
    today = datetime.now().strftime("%Y%m%d")
    partition_spec = f"ds={today},competitor_name={competitor_name_en}"
    table_name = "summerfarm_ds.spider_youzan_product_result_df"
    raw_table_name = "summerfarm_ds.spider_youzan_product_raw_result_df"

    result = write_pandas_df_into_odps(df_exploded, table_name, partition_spec)
    result = result and write_pandas_df_into_odps(
        all_products_raw_df, raw_table_name, partition_spec
    )
    return len(df_exploded)


count = scrapy_and_save_into_odps(
    app_and_kdt_id="app_id=wx3e2e3761ccf9bcae&kdt_id=128798418",
    brand_name="优享鲜焙",
    root_cate_alias="rP1ae2bqTU",
    competitor_name_en="youxiangxianbei",
)
count = count + scrapy_and_save_into_odps(
    app_and_kdt_id="app_id=wx3529eb8a969d738d&kdt_id=98595742",
    brand_name="料料活子",
    root_cate_alias="ucxcweqvk4",
    competitor_name_en="liaoliaohuozi",
)

two_weeks_ago = (datetime.now() - timedelta(14)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select count(*) cnt,ds,competitor_name,COUNT(DISTINCT skuid) skuids,COUNT(DISTINCT alias) alias_cnt
from summerfarm_ds.spider_youzan_product_result_df 
where ds >='{two_weeks_ago}' 
group by competitor_name,ds 
order by ds desc,competitor_name;"""
)


if count > 0:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    logging.info(f"===new_record===[优享鲜焙,料料活子], 商品数:{count}")
else:
    logging.info(f"[优享鲜焙,料料活子], 写入ODPS失败")
