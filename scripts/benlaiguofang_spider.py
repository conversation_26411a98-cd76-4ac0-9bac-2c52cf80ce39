#!/usr/bin/env python
# coding: utf-8

# In[ ]:


# 写入odps
import requests
import json
import hashlib
import time
from datetime import datetime, timedelta
import pandas as pd
import os
from odps import ODPS, DataFrame
from odps.accounts import StsAccount
from scripts.proxy_setup import get_remote_data_with_proxy_json,logging
import traceback
import concurrent.futures
import threading

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235

headers = {
    "Host": "guofangapi.lpq1688.com",
    "Connection": "keep-alive",
    "content-type": "application/json",
    "Accept-Encoding": "gzip,compress,br,deflate",
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.59(0x18003b2c) NetType/4G Language/zh_CN",
    "Referer": "https://servicewechat.com/wxd6c6696aa2913f8c/43/page-frame.html",
}
brand_name = "本来果坊"
competitor_name_en = "benlaiguofang"

logging.info(f"{timestamp_of_now}, headers:{headers}")


# In[ ]:


# 用以下代码实现登录
import requests # 确保requests库已导入

# 创建一个会话对象，它会自动管理cookies
session = requests.Session()

# 登录API的完整URL
login_url = 'https://guofangapi.lpq1688.com/IAccount/Login?Source=1&Version=3.8.6&Client=Applets&DeviceID=67FF8930-8801-43AC-8C76-0F3116AE1556&LoginType=2&customerID=***********&md5Pwd=e10adc3949ba59abbe56e057f20f883e'

# 使用会话对象发送GET请求，自动处理cookies
cookies = None
cookies_map = {}
try:
    response = session.get(url=login_url, headers=headers, timeout=10)
    response.raise_for_status() # 检查HTTP状态码，如果不是2xx则抛出异常
    token = response.json()
    cookies = response.cookies
except requests.exceptions.RequestException as e:
    logging.info(f"登录请求失败: {e}")
    token = None # 登录失败时将token设为None

# 打印登录结果
logging.info(f"登录结果: {token}")
if cookies:
    for cookie in cookies:
        logging.info(f"获取到的cookies: {cookie.name}={cookie.value}")
        cookies_map[cookie.name] = cookie.value
else:
    logging.info("未获取到cookies")

# 注意：此处的session对象已包含登录API返回的cookies，后续所有需要认证的API调用都应使用此session对象
# 例如，将 get_remote_data_with_proxy_json 替换为 session.get() 或 session.post()


# In[ ]:


import pandas as pd # 确保pandas库已导入

# 定义需要获取的madeIn类型：0代表国产，1代表进口
made_in_types = {
    0: "国产",
    1: "进口"
}

# 初始化一个空的DataFrame列表，用于存储不同madeIn类型的结果
all_categories_dfs = []

for made_in_value, made_in_name in made_in_types.items():
    # 根据madeIn值构建分类列表API的完整URL
    category_list_api_url = f'https://guofangapi.lpq1688.com/ICategory/C1ListNew?Source=1&Version=3.8.6&Client=Applets&DeviceID=67FF8930-8801-43AC-8C76-0F3116AE1556&madeIn={made_in_value}&isRefresh=0'

    logging.info(f"正在获取 {made_in_name} 分类列表...")
    try:
        # 使用之前登录成功的session对象发送请求，session会自动携带cookies
        response = session.get(url=category_list_api_url, headers=headers, timeout=10)
        response.raise_for_status() # 检查HTTP状态码，如果不是2xx则抛出异常
        category_data = response.json()

        # 检查响应数据结构，假设分类数据在 'data' 键下且为列表
        if category_data and 'data' in category_data and isinstance(category_data['data'], list):
            current_category_df = pd.DataFrame(category_data['data'])
            # 添加一个新列来标识是国产还是进口
            current_category_df['madeInType'] = made_in_name
            current_category_df['madeIn'] = made_in_value
            all_categories_dfs.append(current_category_df)
            logging.info(f"成功获取 {made_in_name} 分类列表并转换为DataFrame。")
        else:
            logging.info(f"获取 {made_in_name} 分类列表成功，但数据结构不符合预期或数据为空。")
            logging.info(f"原始响应数据: {category_data}")

    except requests.exceptions.RequestException as e:
        logging.info(f"获取 {made_in_name} 分类列表请求失败: {e}")

# 将所有获取到的DataFrame合并成一个
if all_categories_dfs:
    category_df = pd.concat(all_categories_dfs, ignore_index=True)
    logging.info("所有分类列表已合并。")
else:
    category_df = pd.DataFrame() # 如果没有获取到任何数据，则初始化一个空的DataFrame
    logging.info("未能获取任何分类列表。")

# 将DataFrame作为该单元格的输出
category_df


# In[20]:


import pandas as pd
import requests

# 首先，将c1和c2分类信息扁平化，以便后续遍历
category_with_c2 = []

# 遍历category_df的每一行，提取c1分类信息和其下的c2子分类信息
for index, row in category_df.iterrows():
    c1_name = row['c1Name']
    c1_sys_no = row['c1SysNo']
    made_in_type = row['madeInType']
    made_in = row['madeIn']
    c2_list = row['c2List'] # 获取当前c1分类下的c2子分类列表

    # 如果c2List不为空，则遍历c2子分类并添加到结果列表
    if c2_list:
        for c2_item in c2_list:
            category_with_c2.append({
                'category_id': c1_sys_no,
                'category_name': c1_name,
                'c2_id': c2_item.get('c2sysno'),
                'c2_name': c2_item.get('c2name'),
                'madeInType': made_in_type,
                'madeIn': made_in
            })
    else:
        # 如果c2List为空，则只添加c1分类信息，c2信息为None
        category_with_c2.append({
            'category_id': c1_sys_no,
            'category_name': c1_name,
            'c2_id': None,
            'c2_name': None,
            'madeInType': made_in_type,
            'madeIn': made_in
        })

# 将扁平化的分类信息转换为DataFrame
category_with_c2_df = pd.DataFrame(category_with_c2)

# 初始化一个空的列表，用于存储所有获取到的商品数据
all_products = []

# 商品列表API的基础URL和固定参数
product_list_base_url = 'https://guofangapi.lpq1688.com/IProductList/ListNew?Source=1&Version=3.8.6&Client=Applets&DeviceID=67FF8930-8801-43AC-8C76-0F3116AE1556&query=&sort=3&limit=50&extCode=0&filter=&sType=1&isNeedSysNo=true'
page_limit = 50 # 每页商品数量

logging.info("开始获取各分类下的商品列表...")

# 遍历扁平化的分类DataFrame的每一行
for index, row in category_with_c2_df.iterrows():
    c1_sys_no = row['category_id']
    c1_name = row['category_name']
    c2_sys_no = row['c2_id']
    c2_name = row['c2_name']
    made_in_type = row['madeInType']
    made_in = row['madeIn']

    # 根据c2_id是否存在设置isNeedC2SysNo参数
    is_need_c2_sys_no = 'true' if pd.notna(c2_sys_no) else 'false'

    # 构建当前分类的商品列表API URL前缀
    current_category_product_url = f"{product_list_base_url}&c1SysNo={c1_sys_no}&madeIn={made_in}&isNeedC2SysNo={is_need_c2_sys_no}"
    if pd.notna(c2_sys_no):
        current_category_product_url += f"&c2SysNo={int(c2_sys_no)}" # c2_sys_no可能为浮点数，转换为整数

    offset = 0 # 初始化分页偏移量
    while True:
        # 构建带偏移量的完整URL
        full_product_url = f"{current_category_product_url}&offset={offset}"

        category_display_name = f"{c1_name}"
        if c2_name:
            category_display_name += f" - {c2_name}"

        logging.info(f"正在获取 {made_in_type} - {category_display_name} (c1SysNo={c1_sys_no}, c2SysNo={c2_sys_no if pd.notna(c2_sys_no) else 'N/A'}) 第 {offset // page_limit + 1} 页商品...")

        try:
            # 使用之前登录成功的session对象发送请求
            response = session.get(url=full_product_url, headers=headers, timeout=10)
            response.raise_for_status() # 检查HTTP状态码
            product_data = response.json()

            # 检查响应数据结构，根据API返回示例，商品列表在 'data' -> 'productList' 键下
            products_on_page = product_data.get('data', {}).get('productList', [])

            if not products_on_page:
                logging.info(f"获取 {category_display_name} 第 {offset // page_limit + 1} 页商品：无更多商品。")
                break # 没有更多商品，退出分页循环

            # 构建完整的分类名称
            full_category_name = f"{made_in_type} - {category_display_name}"

            for product in products_on_page:
                # 为每个商品添加分类信息
                product['category_id'] = c1_sys_no
                product['category_name'] = c1_name
                product['c2_id'] = c2_sys_no
                product['c2_name'] = c2_name
                product['madeInType'] = made_in_type
                product['madeIn'] = made_in
                # 添加完整的分类名称
                product['category_full_name'] = full_category_name
                all_products.append(product)

            logging.info(f"成功获取 {category_display_name} 第 {offset // page_limit + 1} 页 {len(products_on_page)} 件商品。")

            # 如果当前页返回的商品数量小于每页限制，说明已是最后一页
            if len(products_on_page) < page_limit:
                break
            else:
                offset += page_limit # 准备获取下一页

        except requests.exceptions.RequestException as e:
            logging.info(f"获取 {category_display_name} 第 {offset // page_limit + 1} 页商品请求失败: {e}")
            break # 请求失败，跳过当前分类的后续分页
        except KeyError as e:
            logging.info(f"解析 {category_display_name} 第 {offset // page_limit + 1} 页商品数据失败 (KeyError: {e}). 原始响应: {product_data}")
            break # 数据结构不符，跳过当前分类的后续分页
        except Exception as e:
            logging.info(f"获取 {category_display_name} 第 {offset // page_limit + 1} 页商品时发生未知错误: {e}")
            break

logging.info("所有分类下的商品列表已获取完毕。")

# 将所有商品数据转换为DataFrame
all_products_df = pd.DataFrame(all_products)

logging.info(f"商品列表已获取完毕。商品数量: {len(all_products_df)}")



# In[21]:


from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df
# 写入odps
all_products_df['competitor']=brand_name
all_products_df=all_products_df.astype(str)

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

result = write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name  order by ds desc limit 50""")
logging.info(df.to_string())

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    logging.info(f"===new_record==={brand_name}, 商品数:{len(all_products_df)}")
else:
    logging.info(f"{brand_name}, 写入ODPS失败")

# In[ ]:




