import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy,
    write_pandas_df_into_odps,
    logging,
    get_odps_sql_result_as_df,
    THREAD_CNT,
)

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

headers = {"Referer": "https://servicewechat.com/wx430dcb474584da26/12/page-frame.html"}
brand_name = "长盈鲜焙"
competitor_name_en = "changyingxianbei"
timestamp_of_now = int(datetime.now().timestamp()) * 1000 - 512

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")

logging.info(time_of_now)
# 登录
from urllib.parse import unquote

username = "17729941198"

url = "https://bshop.guanmai.cn/login"
login_response = requests.post(
    url,
    headers=headers,
    data={"username": username, "password": "aa123456"},
    verify=False,
)

after_login_cookie = {}
# Print all the cookies set by the server
for cookie in login_response.cookies:
    logging.info(f"{cookie.name}: {cookie.value}")
    after_login_cookie[cookie.name] = cookie.value

logging.info(f"response cookie:{after_login_cookie}")
login_response_data = login_response.json()
logging.info(login_response_data, unquote(login_response_data["msg"]))
if "sessionid" not in after_login_cookie:
    logging.error(f"爬取失败:{login_response_data['msg']}, username:{username}")
    exit(-1)

## 登录成功后，需要请求一下首页，疑似首页能够初始化session的有效性
after_login_cookie.update({"cms_key": "cygyl", "group_id": "3252"})
after_login_cookie

sessionid = after_login_cookie["sessionid"]

url = f"https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp={timestamp_of_now}"

headers = {
    "authority": "bshop.guanmai.cn",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "en-US,en;q=0.9",
    "cookie": f"cms_key=cygyl; group_id=3252; sessionid={sessionid}; gr_user_id=62c026d8-a829-40c7-823f-d7e38bf255d6; 9beedda875b5420f_gr_session_id=2a97577a-00ae-45a7-8392-4cf0d0fde7cb; 9beedda875b5420f_gr_session_id_sent_vst=2a97577a-00ae-45a7-8392-4cf0d0fde7cb",
    "referer": f"https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp={timestamp_of_now}",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
}

response = requests.get(url, headers=headers, verify=False)

logging.info(response.text[0:100])

logging.info(f"new sessionid:{sessionid}")

# 获取一级类目列表

url = f"https://bshop.guanmai.cn/product/category/get"

categoryList = json.loads(
    get_remote_data_with_proxy(url, headers=headers, cookies=after_login_cookie)
)["data"]

cate_list_df = pd.DataFrame(categoryList)
logging.info(f"一级类目的字段名字：{cate_list_df.columns}")

# 解析所有二级类目；
all_second_category = []
for first in categoryList:
    first_obj = {
        "first_category_name": first["name"],
        "first_category_url": first["url"],
    }
    for second in first["children"]:
        second.update(first_obj)
        all_second_category.append(second)
        logging.info(second)


# 根据二级类目获取商品列表
def get_products_for_second_cate(category_id="B955624"):
    url = f"https://bshop.guanmai.cn/product/sku/get?level=2&category_id={category_id}"

    product_list = json.loads(
        get_remote_data_with_proxy(url, headers=headers, cookies=after_login_cookie)
    )["data"]
    return product_list


# 获取所有的商品；
def get_products_for_second_cate(category_id="B955624"):
    url = f"https://bshop.guanmai.cn/product/sku/get?level=2&category_id={category_id}"

    product_list = requests.get(
        url, headers=headers, cookies=after_login_cookie, verify=False
    ).json()["data"]
    return product_list


all_products = []
all_skus = []
for second in all_second_category:
    sub_product_list = get_products_for_second_cate(second["id"])
    sku_list = []
    for product in sub_product_list:
        product["first_category_id"] = second["first_category_id"]
        product["second_category_id"] = second["id"]
        product["first_category_name"] = second["first_category_name"]
        product["second_category_name"] = second["name"]
        product["first_category_url"] = second["first_category_url"]
        sku_list.extend(product["skus"])
        all_skus.extend(product["skus"])
    names = list(map(lambda x: x["name"], sku_list))
    logging.info(
        f"类目:{second['name']}, 商品数:{len(sub_product_list)}, SKU names:{names}"
    )
    all_products.extend(sub_product_list)


# 根据商品ID获取详情
def get_product_detail(spu_id="C15092196"):
    detail_url = f"https://bshop.guanmai.cn/product/sku/detail?spu_id={spu_id}"
    product_detail = json.loads(
        get_remote_data_with_proxy(detail_url, headers={}, cookies=after_login_cookie)
    )["data"]
    return product_detail


all_sku_list = []
for spu in all_products:
    spu["spu_id"] = spu["id"]
    for sku in spu["skus"]:
        sku["sku_id"] = sku["id"]
        sku.update(spu)
        all_sku_list.append(sku)

all_sku_list_df = pd.DataFrame(all_sku_list)
logging.info(
    all_sku_list_df.head(20)[
        ["sku_id", "sale_price", "std_sale_price", "spu_id", "stocks"]
    ]
)
all_products_df = pd.DataFrame(all_products)
logging.info(
    f"spu columns:{all_products_df.columns}\nsku columns:{all_sku_list_df.columns}"
)

# 写入odps
all_sku_list_df["competitor"] = brand_name
all_products_df["competitor"] = brand_name
all_sku_list_df["request_time"] = time_of_now
all_products_df["request_time"] = time_of_now

all_sku_list_df = all_sku_list_df.astype(str)
all_products_df = all_products_df.astype(str)

today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = "summerfarm_ds.spider_guanmai_product_result_df"
raw_table_name = "summerfarm_ds.spider_guanmai_product_raw_result_df"

write_result = write_pandas_df_into_odps(all_sku_list_df, table_name, partition_spec)
write_result = write_result and write_pandas_df_into_odps(
    all_products_df, raw_table_name, partition_spec
)

df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(spu_id) spu_cnt
                                  ,count(distinct sku_id) sku_cnt
                                  ,count(distinct case when sale_price>0 then sku_id end) has_price_skus
                                  from {table_name} 
                                  where ds>='{days_30}'
                                  group by ds,competitor_name
                                  order by ds,competitor_name"""
)

logging.info(f"成功了！{datetime.now()}\n{df}")
if write_result:
    logging.info(f"===new_record==={brand_name}, 商品数:{len(all_sku_list)}")
else:
    logging.info(f"===new_record==={brand_name}, 写入ODPS错误")
