import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy_json,
    get_remote_data_with_proxy,
    write_pandas_df_into_odps,logging,
    get_odps_sql_result_as_df,
    THREAD_CNT,
)
import os
import re

pattern = re.compile(r"零食|耗材|买过多次|礼盒")

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# 多区域爬取

region_list = [
    # {
    #     "brand_name": "标果-杭州",
    #     "region_name": "hangzhou",
    #     "headers": {
    #         "token": "d962d0ba06514ffa8b80a335d851563f",
    #         "sid": "7731297",
    #         "referer": "https://servicewechat.com/wx6fffdc1a67a2acb6/156/page-frame.html",
    #         "time": "1702521175012",
    #     },
    # },
    {
        "brand_name": "标果工厂-长沙",
        "region_name": "changsha",
        "headers": {
            "token": "5b0c07e2078e46d4b92423e9d0eca1b2",
            "sid": "8200918",
            "referer": "https://servicewechat.com/wx6fffdc1a67a2acb6/156/page-frame.html",
            "time": "1702521175012",
        },
    },
]


competitor_name_en = "biaoguo"

logging.info(f"{time_of_now}, headers:{region_list}")
days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")


def get_full_category_list(category_list_df):
    # Merge the DataFrame with itself multiple times to achieve the desired result
    category_list_full_df = category_list_df.merge(
        category_list_df,
        how="left",
        left_on="parentId",
        right_on="id",
        suffixes=("", "_b"),
    )

    category_list_full_df = category_list_full_df.merge(
        category_list_df,
        how="left",
        left_on="parentId_b",
        right_on="id",
        suffixes=("", "_c"),
    )

    category_list_full_df = category_list_full_df.merge(
        category_list_df,
        how="left",
        left_on="parentId_c",
        right_on="id",
        suffixes=("", "_d"),
    )

    # Select the required columns and concatenate the category names
    category_list_full_df = category_list_full_df[
        category_list_full_df["categoryLevel"] == 4
    ]
    category_list_full_df["category_path"] = (
        category_list_full_df["categoryName_d"]
        + "_"
        + category_list_full_df["categoryName_c"]
        + "_"
        + category_list_full_df["categoryName_b"]
        + "_"
        + category_list_full_df["categoryName"]
    )

    # Select the final DataFrame with desired columns
    category_list_full_df = category_list_full_df[
        ["id", "categoryLevel", "parentId", "categoryName", "code", "category_path"]
    ]
    logging.info(f"category_list_full_df head(1):{category_list_full_df.head(1)}")
    return category_list_full_df


size_per_page = 20


def get_products_for_category(categoryId=9, current=1, headers={}):
    url = (
        "https://api.guohe.biaoguoworks.com/pear/api/h5/store/goods/search-by-category"
    )
    data = {
        "current": current,
        "size": size_per_page,
        "frontFourCategoryIds": [categoryId],
        "filterParamList": [],
        "getFilter": True,
    }
    json_result = get_remote_data_with_proxy_json(url, headers=headers, json=data)
    if (
        json_result is not None
        and json_result["content"] is not None
        and json_result["content"]["page"] is not None
        and json_result["content"]["page"]["records"] is not None
    ):
        return json_result["content"]["page"]["records"]
    else:
        logging.info(f"\n\n不正常的结果categoryId:{categoryId} :{json_result}\n\n")
        return []


product_id_cache = {}


def get_product_detail_with_id(goodsObj={}, headers={}, product_with_detail_list=[]):
    global product_id_cache
    cache_key = f"sid:{headers['sid']}-{goodsObj['id']}"
    if cache_key in product_id_cache:
        logging.info(f"已经获取过了:{cache_key}")
        return None

    url = "https://api.guohe.biaoguoworks.com/pear/api/h5/store/goods/detail"
    product_detail = get_remote_data_with_proxy_json(
        url, headers=headers, json={"goodsId": goodsObj["id"], "sortIndex": 1}
    )["content"]
    product_with_detail_list.append(product_detail)
    goodsObj["goodsProps"] = product_detail["prop"]
    goodsObj["categoryAttributeStandardList"] = product_detail[
        "categoryAttributeStandardList"
    ]
    product_id_cache[cache_key] = True
    return product_detail


def spide_with_headers(region_headers):
    logging.info(f"标果爬虫开始了:{region_headers}")
    headers = region_headers["headers"]
    category_list = get_remote_data_with_proxy_json(
        url="https://api.guohe.biaoguoworks.com/pear/api/h5/store/front-category/list",
        headers=headers,
    )["content"]
    category_list_df = pd.DataFrame(category_list)
    category_list_full_df = get_full_category_list(category_list_df)
    logging.info(category_list_full_df.head(1))

    product_list_all = []

    for index, category in category_list_full_df.iterrows():
        products_of_category = []
        logging.info(f"fetching: {category.to_dict()}, {category['category_path']}")

        if "nan" == f"{category['category_path']}":
            logging.info(f"类目似乎不合法!!")

        matches = pattern.findall(f"{category['category_path']}")
        if matches is not None and len(matches) > 0:
            logging.info(f"这个类目不需要爬取:{pattern}, category:{category}")
            continue
        categoryId = category["id"]
        cate = {
            "category_code": category["code"],
            "category_path": category["category_path"],
        }
        current = 1
        sub_list = get_products_for_category(
            categoryId=categoryId, current=current, headers=headers
        )
        products_of_category.extend(sub_list)
        size = len(sub_list)
        while size >= size_per_page:
            current = current + 1
            logging.info(
                f"current:{current}, size-of-sub-list:{size}, category:{category['category_path']}"
            )
            sub_list = get_products_for_category(categoryId=categoryId, current=current)
            if sub_list is not None:
                products_of_category.extend(sub_list)
                if len(sub_list) < size_per_page:
                    logging.info(f"{category['category_path']}: 已经获取完毕了")
                    break
            else:
                break

        for product in products_of_category:
            try:
                product["id"] = product["goodsDetailWrapperResultDTO"]["goods"]["id"]
                product["skuCode"] = product["goodsDetailWrapperResultDTO"]["goods"][
                    "skuCode"
                ]
                product.update(cate)
                product_list_all.append(product)
            except Exception as e:
                logging.info(f"product:{product}, e:{e}")

    # 多线程
    product_with_detail_props_list = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        # Submit tasks to the executor
        futures = [
            executor.submit(
                get_product_detail_with_id,
                goodsObj=product,
                headers=headers,
                product_with_detail_list=product_with_detail_props_list,
            )
            for product in product_list_all
        ]
        # Wait for all tasks to complete
        concurrent.futures.wait(futures)

    if len(product_list_all) <= 0:
        logging.info("没有爬取到信息")
        return

    logging.info(product_list_all[0])
    product_list_all_df = pd.DataFrame(product_list_all)

    # 写入odps
    product_list_all_df["competitor"] = region_headers["brand_name"]
    all_products_df = product_list_all_df.astype(str)

    today = datetime.now().strftime("%Y%m%d")
    partition_spec = f"ds={today},competitor_name={region_headers['region_name']}"
    table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_result_new_df"

    write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

    days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
    df = get_odps_sql_result_as_df(
        f"""select ds,competitor,count(*) as recods 
                                from {table_name}
                                where ds>='{days_30}' 
                                group by ds,competitor 
                                order by ds,competitor limit 50"""
    )
    logging.info(df)
    return len(product_list_all_df)


region_names = []
result_cnt = 0
for region in region_list:
    region_names.append(region["brand_name"])
    result_cnt = result_cnt + spide_with_headers(region)

logging.info(f"===new_record==={','.join(region_names)}, 商品数:{result_cnt}")
