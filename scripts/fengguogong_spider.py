import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy_json,
    get_remote_data_with_proxy,
    write_pandas_df_into_odps,
    logging,
    get_odps_sql_result_as_df,
    THREAD_CNT,
)
import os
import re

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
headers = {
    "Accept-Encoding": "gzip, deflate",
    "Content-Type": "application\/x-www-form-urlencoded",
    "Accept-Language": "en-CN;q=1, zh-Hans-CN;q=0.9",
    "Accept": "*\/*",
    "User-Agent": "FengGuogong\/1.0.9 (iPhone; iOS 17.3.1; Scale\/3.00)",
    "Content-Length": "40",
    "Host": "fggapi.fengguog.com",
    "Connection": "keep-alive",
}
brand_name = "蜂果供"
competitor_name_en = "fengguogong"

logging.info(f"{time_of_now}, headers:{headers}")

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")

# 先登录：
# url = "http://fggapi.fengguog.com/cuser/login?mobile=13376812870&passwd=d699ef2f61f04f981e8fdd1e1d58d1af"
# token = get_remote_data_with_proxy_json(url=url, json={})
# logging.info(token)

# 获取类目列表：
# if token["data"] is None or not "shopInfo" in token["data"]:
#     raise Exception(f"爬取失败:{token}")

# shopId = token["data"]["shopInfo"]["id"]
shopId = 5292
url_root = f"http://fggapi.fengguog.com/goods/type/first?shopId=5292&websiteNode=3301"

root_cate_list = get_remote_data_with_proxy_json(
    url="http://fggapi.fengguog.com/goods/type/first?shopId=5292&websiteNode=3301"
)["data"]

# 二级类目
all_second_cate_list = []
all_second_cate_typeCode = {}
for root_cate in root_cate_list:
    typeCode = root_cate["typeCode"]
    second_url = f"http://fggapi.fengguog.com/goods/type/second?shopId={shopId}&typeCode={typeCode}&websiteNode=3301"
    second_cate_list = requests.get(url=second_url).json()["data"]
    for second_cate in second_cate_list:
        second_cate["parentTypeCode"] = typeCode
        second_cate["parentTypeName"] = root_cate["typeName"]
        second_cate["parentCategoryId"] = root_cate["id"]
        all_second_cate_typeCode[second_cate["typeCode"]] = second_cate["typeName"]
    all_second_cate_list.extend(second_cate_list)

logging.info(f"all_second_cate_typeCode:{all_second_cate_typeCode}")


def get_product_by_second_cate(second_cate={}, pageNo=1):
    # 商品列表：
    pageSize = 20
    typeCode = second_cate["typeCode"]
    typeName = second_cate["typeName"]
    url = f"http://fggapi.fengguog.com/goods/list?pageNo={pageNo}&pageSize={pageSize}&shopId={shopId}&typeCode={typeCode}&websiteNode=3301"
    products = requests.get(url=url).json()["data"]["list"]
    logging.info(
        f"typeCode:{typeCode}-{typeName}, pageNo:{pageNo}, 商品数:{len(products)}, url:{url}"
    )
    if len(products) < pageSize:
        return products
    else:
        nested_list = get_product_by_second_cate(
            second_cate=second_cate, pageNo=pageNo + 1
        )
        products.extend(nested_list)
        return products


all_product_list = []
for second_cate in all_second_cate_list:
    # logging.info(f"即将获取类目数据:{second_cate}")
    typeName = second_cate["typeName"]
    product_of_cate = get_product_by_second_cate(second_cate)
    for product in product_of_cate:
        goodTypes = product["goodTypes"].split("@")[0]
        if goodTypes in all_second_cate_typeCode:
            product["categoryName"] = all_second_cate_typeCode.get(goodTypes)
        else:
            product["categoryName"] = typeName
    all_product_list.extend(product_of_cate)

all_product_list_df = pd.DataFrame(all_product_list)
result_cnt = len(all_product_list_df)

# 写入odps
all_product_list_df["competitor"] = brand_name
all_products_df = all_product_list_df.astype(str)

today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_result_df"

write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50"""
)
logging.info(f"成功了!\n{df.to_string()}")
logging.info(f"===new_record==={brand_name}, 商品数:{result_cnt}")
