import requests
import json
import pandas as pd
from datetime import datetime,timedelta
import concurrent.futures
from proxy_setup import get_remote_data_with_proxy,write_pandas_df_into_odps,logging,get_odps_sql_result_as_df,THREAD_CNT
import os

os.environ['PYTHONIOENCODING'] = 'UTF-8'

time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')

headers={'uniacid':'2595','appType':'mini',}
brand_name='坤耀烘焙原料'
competitor_name_en='kunyaohongbei'

logging.info(f"{time_of_now}, headers:{headers}")

days_30=(datetime.now()-timedelta(30)).strftime('%Y%m%d')
logging.info(f"time_of_now:{time_of_now},headers:{headers}")
# 获取所有商品列表

url='https://saas.qxepay.com/index.php/channelApi/good/get-product-list?storeId=2653&lat=26.113972&lng=119.320571'

category_and_goods=requests.get(url=url, headers=headers, verify=False).json()['data']['data']
goods=[]
for data in category_and_goods:
    logging.info(f"类目:{data['name']}, 商品数:{len(data['goods'])}")
    goods.extend(data['goods'])

product_list_all_df=pd.DataFrame(goods)
# 写入odps
product_list_all_df['competitor']=brand_name
all_products_df=product_list_all_df.astype(str)

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

result=write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50""")

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    logging.info(f"===new_record==={brand_name}, 商品数:{len(goods)}")
else:
    logging.info(f"{brand_name}, 写入ODPS失败")