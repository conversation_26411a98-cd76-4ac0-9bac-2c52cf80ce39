import json
import pandas as pd
from datetime import datetime, timedelta
from proxy_setup import (
    write_pandas_df_into_odps,
    get_odps_sql_result_as_df,
    get_remote_data_with_proxy_json,
    get_logger,
)

logger = get_logger(__name__)

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

headers = [
    {
        "brand_name": "答音云仓-成都",
        "hostname": "chengdu",
        "token": "ac979d5f-44d9-4452-b6e0-a9e38c8d6176",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
    },
    {
        "brand_name": "答音云仓-重庆",
        "hostname": "bd",
        "token": "5319aeee-58ed-4f97-a28f-91da3c4f7575",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
    },
]
# brand_name = "答音云仓"
competitor_name_en = "dayinyuncang"
timestamp_of_now = int(datetime.now().timestamp()) * 1000 - 512

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")


# 获取一级类目列表
# Convert the dictionary into a JSON object
json_obj = {"limit": 99, "page": 1, "type": 1}

logger.info(f"json_obj:{json_obj}")


def get_second_category(firstCate={"id": 18, "name": "乳制品"}, headers=None):
    all_second_cate_list = []
    hostname = headers["hostname"]

    reanmed_first_cate = {
        "first_category_id": firstCate["id"],
        "first_category_name": firstCate["name"],
    }
    json_obj = {"limit": 99, "page": 1, "type": 1, "pid": firstCate["id"]}
    url = f"https://{hostname}.dayinyuncang.com/api/product/category"

    secondCategoryList = get_remote_data_with_proxy_json(
        url=url, data=json_obj, post=True, headers=headers
    )["data"]["data"]
    logger.info(f"secondCategoryList:{secondCategoryList}")
    for cate in secondCategoryList:
        cate.update(reanmed_first_cate)
    all_second_cate_list.extend(secondCategoryList)
    return secondCategoryList


def get_all_products(headers):
    hostname = headers["hostname"]
    # 先获取一级类目
    first_cate_list_url = f"https://{hostname}.dayinyuncang.com/api/product/category"
    categoryList = get_remote_data_with_proxy_json(
        url=first_cate_list_url,
        data={"limit": 99, "page": "", "type": 1, "pid": ""},
        headers=headers,
        post=True,
    )["data"]["data"]

    pid_list_url = f"https://{hostname}.dayinyuncang.com/api/product/list"
    local_all_products = []
    params = {
        "category_id": 102,
        "limit": 100,
        "page": 1,
        "new_order": 1,
        "price_order": None,
    }
    local_all_second_cate_list = []
    for first in categoryList:
        logger.info(f"爬取{headers['brand_name']}一级类目:{first}")
        second_categories = get_second_category(firstCate=first, headers=headers)
        local_all_second_cate_list.extend(second_categories)

    for cate in local_all_second_cate_list:
        params["category_id"] = cate["id"]
        cate_obj = {
            "first_category_id": cate["first_category_id"],
            "first_category_name": cate["first_category_name"],
            "second_category_id": cate["id"],
            "second_category_name": cate["name"],
            "brand_name": headers["brand_name"],
        }
        products = get_remote_data_with_proxy_json(
            pid_list_url, data=params, post=True, headers=headers
        )["data"]["data"]
        if len(products) > 0:
            for product in products:
                product.update(cate_obj)
            local_all_products.extend(products)
            logger.info(f"{cate['name']} 商品数量:{len(products)}")
        else:
            logger.info(f"{cate['name']} 没有商品！！")
    logger.info(f"{headers['brand_name']} 的商品个数:{len(local_all_products)}")
    return local_all_products


all_products = []
for header in headers:
    brand_name = header["brand_name"]
    products = get_all_products(headers=header)
    all_products.extend(products)


all_products_df = pd.DataFrame(all_products)
all_products_raw = []
for i, obj in enumerate(all_products):
    all_products_raw.append(
        {
            "id": obj["id"],
            "json_content": json.dumps(obj),
            "brand_name": obj["brand_name"],
        }
    )

all_products_raw_df = pd.DataFrame(all_products_raw)
all_products_df = all_products_df.astype(str)

today = datetime.now().strftime("%Y%m%d")

# partition_spec = f"ds={today},competitor_name={competitor_name_en}" # Remove single competitor_name
table_name = "summerfarm_ds.spider_dayinyuncang_product_result_df"
table_raw_name = "summerfarm_ds.spider_dayinyuncang_product_raw_df"
column_set = set(all_products_df.columns)
logger.info(f"all_products columns:{column_set}")
logger.info(f"本地爬取共{len(all_products)} 个商品:{time_of_now}")

brand_names = ", ".join([header["brand_name"] for header in headers])
all_products_df["competitor"] = all_products_df[
    "brand_name"
]  # Use extracted brand_name
all_products_raw_df["competitor"] = all_products_raw_df["brand_name"]
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
# 先把原始数据保存
result = write_pandas_df_into_odps(all_products_raw_df, table_raw_name, partition_spec)
# 再把清洗后的数据保存
result = result and write_pandas_df_into_odps(
    all_products_df, table_name, partition_spec
)


df = get_odps_sql_result_as_df(
    """select ds,competitor_name,count(*) as recods,brand_name
    from summerfarm_ds.spider_dayinyuncang_product_result_df 
    where ds>='20240205' group by ds,competitor_name,brand_name order by ds desc limit 50"""
)

if result:
    logger.info(f"成功了！{datetime.now()},\n{df}")
    logger.info(f"===new_record==={brand_names}, 商品数:{len(all_products_raw)}")
else:
    logger.info(f"写入ODPS失败")
