#!/usr/bin/env python
# coding: utf-8

# In[ ]:


# 写入odps
from datetime import datetime, timedelta
import pandas as pd
from odps import ODPS, DataFrame
from odps.accounts import StsAccount
from scripts.proxy_setup import get_remote_data_with_proxy_json,logging

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235

headers = {
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN",
}
brand_name = "绿神送果-东莞"
competitor_name_en = "lvshensongguo"

logging.info("即将爬取:%s, %s", brand_name, competitor_name_en)


# In[ ]:


# import pymysql
# import os

# mysql_host = os.getenv("COSFODB_HOST_NAME", "mysql-8-public.summerfarm.net")
# logging.info(f'using mysql host:{mysql_host}')

# # Function to establish a database connection
# def get_data_from_mysql(query: str = ""):
#     conn = pymysql.connect(
#         host=mysql_host,
#         user="test",
#         password="xianmu619",
#         port=3307,
#         db="front_db",
#         charset="utf8mb4",
#         cursorclass=pymysql.cursors.DictCursor,
#     )
#     try:
#         with conn.cursor() as cursor:
#             cursor.execute(query)
#             rows = cursor.fetchall()
#             return rows
#     except Exception as e:
#         logging.error(f"从数据库获取登录token失败:{e}")
#         raise e
#     finally:
#         conn.close()


# query = "select * from app_req_record where app_name = 'baofeng' limit 50"
# req_info_list = get_data_from_mysql(query)
# logging.info(f"data from mysql:{req_info_list}")
# if len(req_info_list) <= 0:
#     raise Exception(f"未能从数据库获取到登录信息,SQL:{query}")


# In[15]:


headers = {
    "Accept": "*\/*",
    "token": "6d3eeba5-81cc-48c1-be09-c3fa31eff89c",
    "User-Agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Mobile\/15E148 Html5Plus\/1.0 (Immersed\/20) uni-app",
    "Accept-Encoding": "gzip, deflate, br",
    "Accept-Language": "en-US,en;q=0.9",
    "Connection": "keep-alive",
    "Host": "lvshen.wanxiangshengxian.com",
}
cate_url = "https://lvshen.wanxiangshengxian.com/api/Category/GetCategory"

category_list = get_remote_data_with_proxy_json(url=cate_url, headers=headers).get('data').get('level')
category_list


# In[16]:


from typing import List, Dict, Any


def fetch_category_product(category_id="1,all", page=1) -> List[Dict[str, Any]]:
    url = f"https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page={page}&param={category_id}"
    products = (
        get_remote_data_with_proxy_json(url=url, headers=headers)
        .get("data", {})
        .get("goods", [])
    )
    if len(products) >= 10:
        logging.info(f"类目有超过10个商品, 继续爬取...page:{page+1}")
        products_more = fetch_category_product(category_id=category_id, page=page + 1)
        products.extend(products_more)
    return products


all_products = []
for cate in category_list:
    cate_name = cate["name"]
    if "全部" == cate_name:
        logging.warning(f"我们不爬全部类目，用二级类目来爬...:{cate}")
        continue
    products = fetch_category_product(category_id=cate["param"])
    if len(products) <= 0:
        logging.error(f"爬取失败:{cate}")
        continue
    for prod in products:
        prod["category_name"] = cate_name
    all_products.extend(products)

all_products_df = pd.DataFrame(all_products)

all_products_df.head(10)


# In[ ]:


# def fetch_product_detail(goods_id=45):
#     url = f"https://lvshen.wanxiangshengxian.com/api/Goods/goods_detail?goods_id={goods_id}&type=common"
#     good_detail = get_remote_data_with_proxy_json(url=url, headers=headers).get("data")
#     return good_detail


# d = fetch_product_detail()
# d


# In[ ]:


from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df
# 写入odps
all_products_df.drop_duplicates(subset='goods_id', inplace=True)
all_products_df=all_products_df.astype(str)
all_products_df['competitor']=brand_name

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50""")
logging.info(df.to_string())
logging.info(f"===new_record==={brand_name}, 商品数:{len(all_products_df)}")


# In[ ]:





# In[ ]:




