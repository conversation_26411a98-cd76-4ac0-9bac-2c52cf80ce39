import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy_json,
    get_remote_data_with_proxy,
    write_pandas_df_into_odps,logging,
    get_odps_sql_result_as_df,
    THREAD_CNT,
)
import os
import re

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
headers={'uniacid':'2595','appType':'mini','Referer':'https://servicewechat.com/wx92c8f2cd458916b5/36/page-frame.html',}
brand_name='品尚坊'
competitor_name_en='pinshangfang'

logging.info(f"{time_of_now}, headers:{headers}")

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")

# 先登录：
login_json={
  "userName": "13918652648",
  "password": "chenmo1125",
  "companyName": "品尚坊水果批发配送",
  "themeColor": "9EC741",
  "groupDbid": "2529095",
  "openId": "oFiQ45ZGN7TKZTZGPKs7RYN-a8KA",
  "appId": "wxda0ba60b90801190",
  "client": "miniApp",
  "relayState": "/app/index.html",
  "service": "ydh-agent",
  "nickName": "",
  "tag": "2529095",
  "dbid": "2529095",
  "subDbid": "",
  "showBack": "1",
  "backhash": "page%3DMine",
  "loginLogoImage": "",
  "loginBgImage": "",
  "loginBtnColor": "#9EC741",
  "loginBtnTextColor": "#FFFFFF",
  "loginTextColor": "#666666",
  "extraAction": "binding",
  "limitClient": 1,
  "isWechat": False,
  "loginServerType": 1
}

url='https://sso.dinghuo123.com/jwtToken'
token=get_remote_data_with_proxy_json(url=url, json=login_json)
logging.info(token)

# 获取类目列表：
jwtToken=token['data']["jwtToken"]
headers={
    "referer":f"https://agent.dinghuo123.com/app/index?loginToken={jwtToken}&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6",
    "authorization":f"{jwtToken}",
}
category_list_url="https://agent.dinghuo123.com/app/goods?action=productTypeWithImg&_=*************"

category_list=get_remote_data_with_proxy_json(url=category_list_url, headers=headers)

def get_products_of_category(id=********, currentPage=1):
    url = f"https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage={currentPage}&productTypeId={id}&_=*************"
    products=get_remote_data_with_proxy_json(url=url, headers=headers)
    if products is None:
        return []
    products=products['data']['summaryList']
    if products is None or len(products)<20:
        return products
    # 递归查询
    products.extend(get_products_of_category(id, currentPage+1))
    return products

logging.info(get_products_of_category())

all_products=[]
for cate in category_list['data']:
    currentPage=1
    sub_list=get_products_of_category(cate['id'], currentPage=currentPage)
    if sub_list is None:
        continue
    if len(sub_list)>0:
        for product in sub_list:
            product['categoryName'] = cate['name']
        all_products.extend(sub_list)



all_product_list_df = pd.DataFrame(all_products)
result_cnt = len(all_product_list_df)

# 写入odps
all_product_list_df["competitor"] = brand_name
all_products_df = all_product_list_df.astype(str)

today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_result_df"

write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50"""
)
logging.info(df)
logging.info(f"===new_record==={brand_name}, 商品数:{result_cnt}")