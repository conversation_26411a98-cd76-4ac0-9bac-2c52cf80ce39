#!/usr/bin/env python
# coding: utf-8

# In[ ]:


# 写入odps
from datetime import datetime, timedelta
import pandas as pd
from odps import ODPS, DataFrame
from odps.accounts import StsAccount
from scripts.proxy_setup import get_remote_data_with_proxy_json,logging

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235

headers = {
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN",
}
brand_name = "壹生鲜果-杭州"
competitor_name_en = "yishengxianguo"

logging.info("即将爬取:%s, %s", brand_name, competitor_name_en)

import requests

def login_dinghuoyuan(username, password, code, uuid):
    """
    Login to dinghuoyuan with username, password, code, and uuid.

    Args:
        username (str): username
        password (str): password
        code (str): code
        uuid (str): uuid

    Returns:
        requests.Response: response object
    """
    url = "https://www.dinghuoyuan.com/c_concentrate/login/login4Customer"
    data = {"username": username, "password": password, "code": code, "uuid": uuid}
    headers = {
        "content-type": "application/x-www-form-urlencoded",
    }
    response = requests.post(url, data=data, headers=headers)
    return response


# Example usage:
response = login_dinghuoyuan(
    username="18258841203",
    password="wjf123456",
    code="0c1R8Ikl2Dl62f4Gwwll2QRJlQ0R8IkV",
    uuid="null",
)
token = response.json().get("data", {}).get("token", "")
logging.info(f"login token:{token}")


# In[ ]:


headers = {
    "LoginSource": "WSC_APPLET",
    "Authorization": f"Bearer {token}",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF MacWechat/3.8.7(0x13080712) XWEB/1191",
    "TenantId": "1506987423156953090",
    "Content-Type": "application/x-www-form-urlencoded",
    "xweb_xhr": "1",
    "AppId": "wx873dd045bfa773c7",
    "version": "",
    "Accept": "*/*",
    "Sec-Fetch-Site": "cross-site",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Dest": "empty",
    "Referer": "https://servicewechat.com/wx873dd045bfa773c7/32/page-frame.html",
    "Accept-Encoding": "gzip, deflate, br",
    "Accept-Language": "en-US,en;q=0.9",
}
cate_url = "https://www.dinghuoyuan.com/c_shop/category/getCategoryTallies"

category_list_response = get_remote_data_with_proxy_json(url=cate_url, headers=headers)
logging.info(f"获取到的类目信息:{category_list_response}")

category_list = category_list_response.get("data")

logging.info(f"category_list:{category_list}")
if category_list is None or len(category_list) <= 0:
    logging.error(f"获取到的category_list为空:{category_list_response}")
    exit(-1)


# In[ ]:


from typing import List, Dict, Any

sub_category_list = []
[
    sub_category_list.extend(category.get("subcategoryTallies"))
    for category in category_list
]
sub_category_list_df = pd.DataFrame(sub_category_list)
sub_category_list_df.head(10)


def fetch_category_product(
    category_id="1507954717705568257", page=1
) -> List[Dict[str, Any]]:
    url = f"https://www.dinghuoyuan.com/c_shop/goods/findPage?page={page}&size=50&categoryId={category_id}&headId=&keyword=&tallyId="
    products = (
        get_remote_data_with_proxy_json(url=url, headers=headers)
        .get("data", {})
        .get("records", [])
    )
    if len(products) >= 50:
        logging.info(f"类目有超过50个商品, 继续爬取...page:{page+1}")
        products_more = fetch_category_product(category_id=category_id, page=page + 1)
        products.extend(products_more)
    return products


all_products = []
for cate in sub_category_list:
    cate_name = cate["subCategoryName"]
    products = fetch_category_product(category_id=cate["subCategoryId"])
    if len(products) <= 0:
        logging.error(f"爬取失败:{cate}")
        continue
    for prod in products:
        prod["categoryName"] = cate_name
    all_products.extend(products)


# In[4]:


all_products_exploded=[]

for prod in all_products:
    for item in prod.get("itemVOS",[]):
        new_item = {}
        for key, value in item.items():
            new_item["item_vo_" + key] = value
        new_item.update(prod)
        del new_item["itemVOS"]
        all_products_exploded.append(new_item)

all_products_df = pd.DataFrame(all_products_exploded)

import re


def camel_to_snake(name):
    """Convert camel case string to snake case string."""
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()


def rename_columns_to_snake_case(df):
    """Rename DataFrame columns from camel case to snake case."""
    df.rename(columns=lambda x: camel_to_snake(x), inplace=True)


rename_columns_to_snake_case(all_products_df)


# In[ ]:


from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df
# 写入odps
all_products_df.drop_duplicates(subset='goods_id', inplace=True)
all_products_df=all_products_df.astype(str)
all_products_df['competitor']=brand_name

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50""")
logging.info(df.to_string())
logging.info(f"===new_record==={brand_name}, 商品数:{len(all_products_df)}")


# In[ ]:





# In[ ]:




