# 写入odps
from datetime import datetime, timedelta
import pandas as pd
from odps import ODPS, DataFrame
from odps.accounts import StsAccount
from proxy_setup import get_remote_data_with_proxy_json,write_pandas_df_into_odps,logging,get_odps_sql_result_as_df

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235

headers = {
    "uniacid": "2595",
    "appType": "mini",
    "Referer": "https://weixin.dinghuo365.com/h5fw/",
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
}
brand_name = "蓝微"
competitor_name_en = "lanwei"

logging.info(f"{timestamp_of_now}, headers:{headers}")

# 先登录：

token = get_remote_data_with_proxy_json(
    url="https://weixin.dinghuo365.com/login/mobileLoginBySmsToWeChat.action?mobile=***********&clientType=client-android-000000&clientVersion=2.0.4&wxLoginType=h5&showRegister=0&password=*********"
)
jwtToken = token["data"]["token"]
logging.info(f"new token:\n{jwtToken}")

import json


all_products=[]
def get_products_by_page(page=1):
    url = "https://weixin.dinghuo365.com/biz/std_mendian/pd/client/v1/queryPds.action"
    data = {
        "orderType": "MERP",
        "page": page,
        "rows": 20,
        "class_id": "-1",
        "period": "all",
        "brand": "",
        "brandIds": "",
        "orderByField": "",
        "sortWay": "asc",
    }
    cookies = {
        "sourceType": "CLIENT",
        "WQSESSIONID": "27E4A77DA514A0ACFBC79B217CA0A03D.10",
        "x-token": jwtToken,
        "tenantId": "6489320774649852103",
    }

    products = get_remote_data_with_proxy_json(url=url, json=data, cookies=cookies)[
        "data"
    ]["products"]
    if products is not None and len(products) >= 20:
        logging.info(f"未取完:{page},继续...")
        sub_list = get_products_by_page(page=page + 1)
        if len(sub_list) > 0:
            products.extend(sub_list)
    return products

all_products=get_products_by_page(1)
all_sku_list=[]
for spu in all_products:
    for unit_info in spu['unit_info']:
        logging.info(unit_info)
        sku_info={}
        for key,value in unit_info.items():
            sku_info[f"sku_{key}"]=value
        sku_info.update(spu)
        del sku_info['unit_info']
        all_sku_list.append(sku_info)

result_cnt=len(all_sku_list)
all_sku_list_df=pd.DataFrame(all_sku_list)

# 写入odps
all_sku_list_df['competitor']=brand_name
all_products_df=all_sku_list_df.astype(str)

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50""")
logging.info(df)
logging.info(f"===new_record==={brand_name}, 商品数:{result_cnt}")