#!/bin/bash

# Docker环境专用的多线程爬虫执行器
# 解决了Docker容器中shell兼容性问题
#
# 优化特性：
# - 使用健壮的进程存活检查 (kill -0)
# - 优雅的进程终止机制 (TERM -> KILL)
# - 详细的进程状态监控和日志
# - 防死锁的并发控制
# - 自动清理僵尸进程和PID文件

set -e  # 遇到错误立即退出

# 配置参数
SCRIPT_DIR="./scripts"
MAX_PARALLEL_JOBS=${MAX_JOBS:-2}
TASK_TIMEOUT=${TASK_TIMEOUT:-7200}  # 单个任务超时时间（秒），默认2小时
RESULTS_DIR="./temp_results_$(date +%s)"
LOG_DIR="./temp_logs_$(date +%s)"
PID_DIR="./temp_pids_$(date +%s)"
START_TIME_DIR="./temp_start_times_$(date +%s)"

# 环境变量设置
export APP_LOG_DIR=$(pwd)
export PYTHONPATH=$PYTHONPATH:$(pwd)

echo "=== Docker多线程爬虫执行器启动 ==="
echo "最大并行任务数: $MAX_PARALLEL_JOBS"
echo "结果目录: $RESULTS_DIR"
echo "日志目录: $LOG_DIR"
echo "PID目录: $PID_DIR"

# 创建临时目录
mkdir -p "$RESULTS_DIR"
mkdir -p "$LOG_DIR"
mkdir -p "$PID_DIR"
mkdir -p "$START_TIME_DIR"

# 优化的清理函数
cleanup() {
    echo "🧹 开始清理临时目录和进程..."
    local cleanup_timeout=10  # 清理超时时间
    local terminated_count=0
    local failed_count=0

    # 优雅地终止所有子进程
    if [ -d "$PID_DIR" ]; then
        echo "📋 检查并终止运行中的任务进程..."

        for pid_file in "$PID_DIR"/*.pid; do
            if [ -f "$pid_file" ]; then
                pid=$(cat "$pid_file" 2>/dev/null || echo "")
                task_name=$(basename "$pid_file" .pid)

                if is_process_alive "$pid"; then
                    echo "🛑 终止任务: $task_name (PID: $pid)"

                    # 先尝试优雅终止
                    if kill -TERM "$pid" 2>/dev/null; then
                        # 等待进程优雅退出
                        local wait_count=0
                        while [ $wait_count -lt $cleanup_timeout ] && is_process_alive "$pid"; do
                            sleep 1
                            wait_count=$((wait_count + 1))
                        done

                        # 如果还没退出，强制终止
                        if is_process_alive "$pid"; then
                            echo "⚡ 强制终止: $task_name (PID: $pid)"
                            kill -KILL "$pid" 2>/dev/null || true
                            sleep 1
                        fi

                        # 最终检查
                        if ! is_process_alive "$pid"; then
                            terminated_count=$((terminated_count + 1))
                            echo "✅ 已终止: $task_name"
                        else
                            failed_count=$((failed_count + 1))
                            echo "❌ 终止失败: $task_name (PID: $pid)"
                        fi
                    else
                        failed_count=$((failed_count + 1))
                        echo "❌ 无法发送终止信号: $task_name (PID: $pid)"
                    fi
                else
                    echo "ℹ️  进程已退出: $task_name (PID: $pid)"
                fi
            fi
        done

        if [ $terminated_count -gt 0 ]; then
            echo "🎯 成功终止 $terminated_count 个进程"
        fi
        if [ $failed_count -gt 0 ]; then
            echo "⚠️  $failed_count 个进程终止失败"
        fi
    fi

    # 清理临时目录
    echo "🗂️  清理临时目录..."
    rm -rf "$RESULTS_DIR" "$LOG_DIR" "$PID_DIR" "$START_TIME_DIR"
    echo "✨ 清理完成"
}

# 设置退出时清理
trap cleanup EXIT INT TERM

# 获取要执行的文件列表
get_python_files() {
    if [ -z "$FILE_TO_EXECUTE" ]; then
        find "$SCRIPT_DIR" -name "*.py" | grep -v proxy_setup | sort
    else
        echo "$SCRIPT_DIR/$FILE_TO_EXECUTE"
    fi
}

# 执行单个Python文件的函数
execute_single_file() {
    local file_path="$1"
    local file_name=$(basename "$file_path")
    local log_file="$LOG_DIR/${file_name}.log"
    local result_file="$RESULTS_DIR/${file_name}.result"
    local pid_file="$PID_DIR/${file_name}.pid"
    local start_time_file="$START_TIME_DIR/${file_name}.start"
    local start_time=$(date +%s)

    # 记录开始时间
    echo "$start_time" > "$start_time_file"

    echo "[$(date '+%H:%M:%S')] 开始执行: $file_name (Shell PID: $$)"

    # 设置该文件专用的日志文件
    export APP_LOG_DIR="$LOG_DIR"
    export SPIDER_LOG_FILE="$log_file"

    # 启动Python进程并记录其PID
    echo "=== 开始时间: $(date) ===" > "$log_file"
    echo "=== 文件: $file_name ===" >> "$log_file"

    # 在后台启动Python进程
    python "$file_path" >> "$log_file" 2>&1 &
    local python_pid=$!

    # 记录Python进程的PID到文件
    echo "$python_pid" > "$pid_file"
    echo "[$(date '+%H:%M:%S')] Python进程已启动: $file_name (Python PID: $python_pid)"

    # 等待Python进程完成
    wait $python_pid
    local exit_code=$?

    # 记录结束信息到日志
    echo "=== 结束时间: $(date) ===" >> "$log_file"
    echo "=== 退出码: $exit_code ===" >> "$log_file"

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    # 分析执行结果
    analyze_result "$file_name" "$log_file" "$result_file" "$duration" "$exit_code"

    echo "[$(date '+%H:%M:%S')] 完成执行: $file_name (耗时: ${duration}s)"

    # 清理PID文件和开始时间文件
    rm -f "$pid_file"
    rm -f "$start_time_file"
}

# 分析单个文件的执行结果
analyze_result() {
    local file_name="$1"
    local log_file="$2"
    local result_file="$3"
    local duration="$4"
    local exit_code="$5"

    local success_count=0
    local brand_name=""
    local last_line=""
    local has_success_marker=false
    local has_error_indicators=false

    if [ -f "$log_file" ]; then
        # 查找成功标记
        if grep -q "===new_record===" "$log_file"; then
            has_success_marker=true
            success_line=$(grep "===new_record===" "$log_file" | tail -1)
            brand_name=$(echo "$success_line" | sed 's/.*===new_record===\([^,]*\).*/\1/' | tr -d ' ')
            success_count=$(echo "$success_line" | grep -o '[0-9]\+' | tail -1)
            if [ -z "$success_count" ]; then
                success_count=0
            fi
        fi

        # 检查是否有明显的错误指示器
        if grep -qi "error\|exception\|failed\|traceback\|登录失败\|爬取失败" "$log_file"; then
            has_error_indicators=true
        fi

        # 获取最后几行作为错误信息
        last_line=$(tail -5 "$log_file" | grep -v "^$" | tail -3 | tr '\n' ' ' | sed 's/[[:space:]]\+/ /g')

        # 如果没有成功标记但退出码为0，尝试其他成功指示器
        if [ "$has_success_marker" = false ] && [ "$exit_code" = "0" ]; then
            # 检查是否有写入ODPS成功的日志
            if grep -q "成功写入odps\|persist.*成功\|写入.*成功" "$log_file"; then
                success_count=1  # 设置为1表示有数据但数量未知
                brand_name=$(echo "$file_name" | sed 's/_spider\.py$//' | sed 's/\.py$//')
            fi
        fi
    fi
    
    # 生成结果JSON
    cat > "$result_file" << EOF
{
    "file_name": "$file_name",
    "brand_name": "$brand_name",
    "exit_code": $exit_code,
    "success_count": $success_count,
    "duration": $duration,
    "timestamp": "$(date '+%Y-%m-%d %H:%M:%S')",
    "last_output": "$last_line",
    "log_file": "$log_file"
}
EOF
    
    echo "[分析] $file_name: 退出码=$exit_code, 成功数=$success_count, 耗时=${duration}s, 成功标记=$has_success_marker, 错误指示器=$has_error_indicators"
}

# 检查进程是否存活（更健壮的实现）
is_process_alive() {
    local pid="$1"
    if [ -z "$pid" ] || [ "$pid" = "0" ]; then
        return 1
    fi

    # 使用kill -0检查进程是否存在
    if kill -0 "$pid" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 获取进程状态信息（用于调试）
get_process_status() {
    local pid="$1"
    if [ -z "$pid" ]; then
        echo "无效PID"
        return
    fi

    # 在macOS和Linux上都可用的方法
    if command -v ps >/dev/null 2>&1; then
        ps -p "$pid" -o pid,ppid,state,etime,command 2>/dev/null || echo "进程不存在"
    else
        echo "无法获取进程状态"
    fi
}

# 检查可疑的长时间运行进程
check_suspicious_processes() {
    local current_time=$(date +%s)
    # 从环境变量获取阈值，默认为7200秒(120分钟)
    local suspicious_threshold=${SUSPICIOUS_THRESHOLD:-7200}

    echo "🔍 检查可疑的长时间运行进程..."

    if [ -d "$PID_DIR" ]; then
        for pid_file in "$PID_DIR"/*.pid; do
            if [ -f "$pid_file" ]; then
                local pid=$(cat "$pid_file" 2>/dev/null || echo "")
                local task_name=$(basename "$pid_file" .pid | sed 's/_spider\.py$//' | sed 's/\.py$//')
                local start_time_file="$START_TIME_DIR/${task_name}.py.start"

                if [ -f "$start_time_file" ]; then
                    local start_time=$(cat "$start_time_file" 2>/dev/null || echo "$current_time")
                    local elapsed=$((current_time - start_time))

                    if [ $elapsed -gt $suspicious_threshold ]; then
                        echo "⚠️  可疑进程: $task_name (PID: $pid, 运行了 ${elapsed}s)"

                        # 检查进程是否真的存在
                        if is_process_alive "$pid"; then
                            echo "   📋 进程状态:"
                            get_process_status "$pid" | sed 's/^/      /'

                            # 检查日志文件最后修改时间
                            local log_file="$LOG_DIR/${task_name}.py.log"
                            if [ -f "$log_file" ]; then
                                local log_age=$((current_time - $(stat -f %m "$log_file" 2>/dev/null || echo "$current_time")))
                                echo "   📄 日志文件最后更新: ${log_age}秒前"
                                echo "   📝 日志文件最后几行:"
                                tail -3 "$log_file" 2>/dev/null | sed 's/^/      /' || echo "      无法读取日志"
                            else
                                echo "   ❌ 日志文件不存在: $log_file"
                            fi
                        else
                            echo "   💀 进程已死亡，但PID文件仍存在"
                            echo "   🧹 清理僵尸PID文件..."
                            rm -f "$pid_file"
                            rm -f "$start_time_file"
                        fi
                        echo ""
                    fi
                fi
            fi
        done
    fi
}

# 等待所有任务完成（优化的PID存活检查）
wait_for_all_tasks() {
    local check_interval=5  # 检查间隔（秒）
    local last_status_time=0
    local last_summary_time=0
    local status_interval=30  # 详细状态报告间隔（秒）
    local summary_interval=15  # 简要状态报告间隔（秒）

    echo "开始监控任务进程状态..."

    while true; do
        local running_count=0
        local running_tasks=""
        local timeout_tasks=""
        local completed_tasks=""
        local current_time=$(date +%s)
        local show_detailed_status=false
        local show_summary_status=false

        # 判断是否需要显示详细状态
        if [ $((current_time - last_status_time)) -ge $status_interval ]; then
            show_detailed_status=true
            last_status_time=$current_time
            # 在详细状态检查时，也检查可疑进程
            check_suspicious_processes
        fi

        # 判断是否需要显示简要状态
        if [ $((current_time - last_summary_time)) -ge $summary_interval ]; then
            show_summary_status=true
            last_summary_time=$current_time
        fi

        if [ -d "$PID_DIR" ]; then
            for pid_file in "$PID_DIR"/*.pid; do
                if [ -f "$pid_file" ]; then
                    pid=$(cat "$pid_file" 2>/dev/null || echo "")
                    task_name=$(basename "$pid_file" .pid | sed 's/_spider\.py$//' | sed 's/\.py$//')

                    if is_process_alive "$pid"; then
                        # 进程仍在运行，检查是否超时
                        start_time_file="$START_TIME_DIR/${task_name}.py.start"
                        if [ -f "$start_time_file" ]; then
                            start_time=$(cat "$start_time_file" 2>/dev/null || echo "$current_time")
                            elapsed=$((current_time - start_time))

                            if [ $elapsed -gt $TASK_TIMEOUT ]; then
                                echo "⚠️  任务超时，强制终止: $task_name (PID: $pid, 运行了 ${elapsed}s)"

                                # 尝试优雅终止，然后强制终止
                                kill -TERM "$pid" 2>/dev/null || true
                                sleep 2
                                if is_process_alive "$pid"; then
                                    kill -KILL "$pid" 2>/dev/null || true
                                fi

                                # 清理文件
                                rm -f "$pid_file"
                                rm -f "$start_time_file"
                                timeout_tasks="$timeout_tasks $task_name"
                                continue
                            fi

                            # 显示详细状态
                            if [ "$show_detailed_status" = true ]; then
                                echo "🔄 运行中: $task_name (PID: $pid, 已运行 ${elapsed}s)"
                            fi
                        fi

                        running_count=$((running_count + 1))
                        if [ -z "$running_tasks" ]; then
                            running_tasks="$task_name"
                        else
                            running_tasks="$running_tasks, $task_name"
                        fi
                    else
                        # 进程已结束，清理相关文件
                        echo "✅ 任务完成: $task_name (PID: $pid 已退出)"
                        rm -f "$pid_file"
                        start_time_file="$START_TIME_DIR/${task_name}.py.start"
                        rm -f "$start_time_file"
                        completed_tasks="$completed_tasks $task_name"
                    fi
                fi
            done
        fi

        # 如果没有运行中的任务，退出循环
        if [ $running_count -eq 0 ]; then
            echo "🎉 所有任务已完成！"
            break
        fi

        # 输出状态信息
        if [ -n "$timeout_tasks" ]; then
            echo "⏰ 已终止超时任务:$timeout_tasks"
        fi

        if [ -n "$completed_tasks" ]; then
            echo "✅ 新完成任务:$completed_tasks"
        fi

        # 只在指定间隔显示等待状态，避免重复日志
        if [ "$show_summary_status" = true ]; then
            echo "📊 等待 $running_count 个任务完成: [$running_tasks]"
        fi

        sleep $check_interval
    done
}

# 控制并发数量（优化的PID存活检查）
control_concurrency() {
    local check_interval=1  # 检查间隔（秒）
    local max_wait_time=300  # 最大等待时间5分钟，防止死锁
    local wait_start_time=$(date +%s)

    while true; do
        local current_jobs=0
        local active_pids=""
        local current_time=$(date +%s)

        # 检查是否等待时间过长
        if [ $((current_time - wait_start_time)) -gt $max_wait_time ]; then
            echo "⚠️  并发控制等待超时，可能存在僵尸进程，强制继续..."
            break
        fi

        if [ -d "$PID_DIR" ]; then
            for pid_file in "$PID_DIR"/*.pid; do
                if [ -f "$pid_file" ]; then
                    pid=$(cat "$pid_file" 2>/dev/null || echo "")
                    task_name=$(basename "$pid_file" .pid)

                    if is_process_alive "$pid"; then
                        current_jobs=$((current_jobs + 1))
                        active_pids="$active_pids $pid($task_name)"
                    else
                        # 进程已结束，清理PID文件
                        echo "🧹 清理已结束进程的PID文件: $task_name (PID: $pid)"
                        rm -f "$pid_file"
                        # 同时清理对应的开始时间文件
                        start_time_file="$START_TIME_DIR/${task_name}.start"
                        rm -f "$start_time_file"
                    fi
                fi
            done
        fi

        if [ $current_jobs -lt $MAX_PARALLEL_JOBS ]; then
            if [ $current_jobs -gt 0 ]; then
                echo "🚀 当前运行 $current_jobs/$MAX_PARALLEL_JOBS 个任务，可以启动新任务"
            fi
            break
        fi

        echo "⏳ 等待任务槽位释放... (当前: $current_jobs/$MAX_PARALLEL_JOBS)$active_pids"
        sleep $check_interval
    done
}

# 主执行逻辑
main() {
    local start_time=$(date +%s)
    local python_files=$(get_python_files)
    local total_files=$(echo "$python_files" | wc -l)

    if [ -z "$python_files" ]; then
        echo "错误: 没有找到要执行的Python文件"
        exit 1
    fi

    echo "准备执行 $total_files 个文件..."

    # 将总文件数写入文件，供监控函数使用
    echo "$total_files" > "$PID_DIR/.total_files"
    
    # 并行执行所有文件
    for file in $python_files; do
        if [ ! -f "$file" ]; then
            echo "警告: 文件不存在: $file"
            continue
        fi
        
        # 控制并发数量
        control_concurrency
        
        # 后台执行
        execute_single_file "$file" &

        echo "已启动: $(basename "$file")"
        sleep 0.2  # 减少延迟，但仍给系统时间创建PID文件
    done
    
    echo "所有任务已启动，等待完成..."
    wait_for_all_tasks
    
    local end_time=$(date +%s)
    local total_duration=$((end_time - start_time))
    
    # 汇总结果
    summarize_results "$total_duration"
}

# 汇总所有执行结果
summarize_results() {
    local total_duration="$1"
    local success_results=""
    local failed_results=""
    local total_success=0
    local total_failed=0
    
    echo "=== 汇总执行结果 ==="
    
    for result_file in "$RESULTS_DIR"/*.result; do
        if [ ! -f "$result_file" ]; then
            continue
        fi
        
        # 读取结果JSON (简单解析)
        local file_name=$(grep '"file_name"' "$result_file" | cut -d'"' -f4)
        local brand_name=$(grep '"brand_name"' "$result_file" | cut -d'"' -f4)
        local exit_code=$(grep '"exit_code"' "$result_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
        local success_count=$(grep '"success_count"' "$result_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
        local duration=$(grep '"duration"' "$result_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
        local timestamp=$(grep '"timestamp"' "$result_file" | cut -d'"' -f4)
        local last_line=$(grep '"last_output"' "$result_file" | cut -d'"' -f4)
        local has_error_indicators=$(grep '"has_error_indicators"' "$result_file" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
        
        # 判断成功条件：
        # 1. 退出码为0且成功数量大于0，或者
        # 2. 退出码为0且没有明显错误指示器且有日志输出
        local is_success=false
        if [ "$exit_code" = "0" ] && [ "$success_count" -gt "0" ]; then
            is_success=true
        elif [ "$exit_code" = "0" ] && [ "$has_error_indicators" = "false" ] && [ -n "$last_line" ]; then
            # 如果退出码正常，没有错误指示器，且有日志输出，认为可能成功
            is_success=true
            if [ "$success_count" = "0" ]; then
                success_count=1  # 设置为1表示可能有数据
            fi
        fi

        if [ "$is_success" = "true" ]; then
            # 格式化成功结果，兼容原有的飞书通知格式
            local display_name="$brand_name"
            if [ -z "$brand_name" ]; then
                display_name="$file_name"
            fi
            success_results="${success_results}${display_name}:::${success_count}, ${timestamp}__EOF__"
            total_success=$((total_success + 1))
            echo "✓ $file_name ($display_name): 成功 ($success_count 条记录, ${duration}s)"
        else
            # 格式化失败结果，包含错误信息和日志
            local error_msg="执行失败"
            if [ "$exit_code" != "0" ]; then
                error_msg="退出码: $exit_code"
            elif [ "$success_count" = "0" ]; then
                error_msg="未爬取到数据"
            fi

            # 清理和截断日志信息，避免过长
            local clean_last_line=""
            if [ -n "$last_line" ]; then
                # 移除特殊字符，截断到200字符
                clean_last_line=$(echo "$last_line" | tr -d '\n\r\t' | sed 's/[[:space:]]\+/ /g' | cut -c1-200)
                # 如果日志为空或只有时间戳，则不显示
                if echo "$clean_last_line" | grep -q "^[[:space:]]*$\|^[[:space:]]*===\|^[[:space:]]*\[.*\]"; then
                    clean_last_line=""
                fi
            fi

            # 构建失败结果，格式：file_name:::error_msg, timestamp|||log_info
            if [ -n "$clean_last_line" ]; then
                failed_results="${failed_results}${file_name}:::${error_msg}, ${timestamp}|||${clean_last_line}__EOF__"
            else
                failed_results="${failed_results}${file_name}:::${error_msg}, ${timestamp}__EOF__"
            fi

            total_failed=$((total_failed + 1))
            echo "✗ $file_name: 失败 ($error_msg, ${duration}s)"
            if [ -n "$clean_last_line" ]; then
                echo "    最后日志: $clean_last_line"
            fi
        fi
    done
    
    echo ""
    echo "=== 执行统计 ==="
    echo "总文件数: $((total_success + total_failed))"
    echo "成功: $total_success"
    echo "失败: $total_failed"
    echo "总耗时: ${total_duration}s ($(($total_duration/60))分$((total_duration%60))秒)"
    echo ""


    
    # 调用飞书通知
    if [ -f "./send_feishu_notification.py" ]; then
        echo "发送飞书通知..."
        python ./send_feishu_notification.py \
            --all_failed_result="$failed_results" \
            --all_success_result="$success_results" \
            --time_cost_in_seconds=$total_duration
    else
        echo "未找到飞书通知脚本，跳过通知"
    fi
}

# 执行主函数
main "$@"
